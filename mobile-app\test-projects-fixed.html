<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Projects Fixed - Flori Construction</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="css/app.css">
    <style>
        .test-container {
            max-width: 1000px;
            margin: 20px auto;
            padding: 20px;
            background: white;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status {
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            font-weight: 500;
        }
        .status.success { background: #d4edda; color: #155724; }
        .status.error { background: #f8d7da; color: #721c24; }
        .status.info { background: #d1ecf1; color: #0c5460; }
        .status.warning { background: #fff3cd; color: #856404; }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #eee;
            border-radius: 8px;
        }
        .log-output {
            background: #f8f8f8;
            border: 1px solid #ddd;
            padding: 15px;
            border-radius: 8px;
            max-height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
        }
        .btn-group {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
            margin: 15px 0;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🔧 Projects Loading Fix Test</h1>
        
        <div class="status info" id="status">
            Ready to test projects loading with enhanced debugging
        </div>

        <div class="test-section">
            <h3>🔑 Authentication</h3>
            <div class="btn-group">
                <button class="btn btn-primary" onclick="testLogin()">
                    <i class="fas fa-sign-in-alt"></i> Login
                </button>
                <button class="btn btn-secondary" onclick="checkAuth()">
                    <i class="fas fa-check"></i> Check Auth
                </button>
                <button class="btn btn-ghost" onclick="clearToken()">
                    <i class="fas fa-trash"></i> Clear Token
                </button>
            </div>
            <div id="auth-info" class="status info" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>🧪 API Tests</h3>
            <div class="btn-group">
                <button class="btn btn-primary" onclick="testPing()">
                    <i class="fas fa-ping-pong-paddle-ball"></i> Test Ping
                </button>
                <button class="btn btn-primary" onclick="testProjectsAPI()">
                    <i class="fas fa-building"></i> Test Projects API
                </button>
                <button class="btn btn-primary" onclick="testDirectFetch()">
                    <i class="fas fa-download"></i> Direct Fetch Test
                </button>
            </div>
        </div>

        <div class="test-section">
            <h3>📋 Projects Manager</h3>
            <div class="btn-group">
                <button class="btn btn-primary" onclick="initializeManager()">
                    <i class="fas fa-cog"></i> Initialize Manager
                </button>
                <button class="btn btn-primary" onclick="loadProjectsTest()">
                    <i class="fas fa-list"></i> Load Projects
                </button>
                <button class="btn btn-secondary" onclick="checkManagerStatus()">
                    <i class="fas fa-info"></i> Check Status
                </button>
            </div>
        </div>

        <!-- Simulate the projects page structure -->
        <div id="projects-page" class="page active">
            <div class="page-header">
                <h2>Projects</h2>
                <button id="add-project-btn" class="btn btn-primary">
                    <i class="fas fa-plus"></i> Add Project
                </button>
            </div>

            <div class="filters">
                <select id="project-type-filter">
                    <option value="">All Projects</option>
                    <option value="completed">Completed</option>
                    <option value="ongoing">Ongoing</option>
                </select>
                <input type="search" id="project-search" placeholder="Search projects...">
            </div>

            <div id="projects-list" class="projects-grid">
                <!-- Projects will be loaded here -->
            </div>

            <div id="projects-pagination" class="pagination">
                <!-- Pagination will be loaded here -->
            </div>
        </div>

        <div class="test-section">
            <h3>📝 Debug Log</h3>
            <button class="btn btn-ghost" onclick="clearLog()">
                <i class="fas fa-eraser"></i> Clear Log
            </button>
            <div id="log-output" class="log-output"></div>
        </div>
    </div>

    <!-- Modal overlay -->
    <div class="modal-overlay" id="modal-overlay">
        <div class="modal-content" id="modal-content"></div>
    </div>

    <!-- Toast container -->
    <div class="toast-container" id="toast-container"></div>

    <!-- Load scripts -->
    <script src="js/app.js"></script>
    <script src="js/projects.js"></script>

    <script>
        // Enhanced logging
        const originalLog = console.log;
        const originalError = console.error;
        const originalWarn = console.warn;
        
        function addToLog(message, type = 'log') {
            const logOutput = document.getElementById('log-output');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] [${type.toUpperCase()}] ${message}\n`;
            logOutput.textContent += logEntry;
            logOutput.scrollTop = logOutput.scrollHeight;
        }
        
        console.log = function(...args) {
            originalLog.apply(console, args);
            addToLog(args.join(' '), 'log');
        };
        
        console.error = function(...args) {
            originalError.apply(console, args);
            addToLog(args.join(' '), 'error');
        };
        
        console.warn = function(...args) {
            originalWarn.apply(console, args);
            addToLog(args.join(' '), 'warn');
        };

        function updateStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            statusDiv.textContent = message;
            statusDiv.className = `status ${type}`;
        }

        function clearLog() {
            document.getElementById('log-output').textContent = '';
        }

        function clearToken() {
            localStorage.removeItem('flori_token');
            if (window.floriAdmin) {
                window.floriAdmin.token = null;
                window.floriAdmin.user = null;
            }
            updateStatus('Token cleared', 'warning');
            console.log('Token cleared from localStorage and floriAdmin');
        }

        async function testLogin() {
            updateStatus('Testing login...', 'info');
            console.log('=== LOGIN TEST START ===');
            
            const username = prompt('Enter username:') || 'admin';
            const password = prompt('Enter password:') || 'admin123';
            
            try {
                console.log('Attempting login with username:', username);
                
                const response = await fetch('../api/auth.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ username, password })
                });
                
                console.log('Login response status:', response.status);
                const data = await response.json();
                console.log('Login response data:', data);
                
                if (data.success) {
                    localStorage.setItem('flori_token', data.token);
                    
                    // Ensure floriAdmin is updated
                    if (window.floriAdmin) {
                        window.floriAdmin.token = data.token;
                        window.floriAdmin.user = data.user;
                        console.log('Updated floriAdmin with token and user');
                    }
                    
                    updateStatus('Login successful! Token saved.', 'success');
                    
                    // Show auth info
                    const authInfo = document.getElementById('auth-info');
                    authInfo.style.display = 'block';
                    authInfo.className = 'status success';
                    authInfo.textContent = `Logged in as: ${data.user.username} (${data.user.role})`;
                    
                } else {
                    throw new Error(data.error || 'Login failed');
                }
            } catch (error) {
                console.error('Login error:', error);
                updateStatus('Login failed: ' + error.message, 'error');
            }
            
            console.log('=== LOGIN TEST END ===');
        }

        async function checkAuth() {
            console.log('=== AUTH CHECK START ===');
            
            const token = localStorage.getItem('flori_token');
            console.log('Token from localStorage:', token ? 'present' : 'missing');
            
            if (window.floriAdmin) {
                console.log('floriAdmin token:', window.floriAdmin.token ? 'present' : 'missing');
                console.log('floriAdmin user:', window.floriAdmin.user ? 'present' : 'missing');
            }
            
            if (!token) {
                updateStatus('No token found - please login first', 'warning');
                return;
            }
            
            try {
                const response = await fetch('../api/auth.php?action=verify', {
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });
                
                const data = await response.json();
                console.log('Auth verification response:', data);
                
                if (data.success) {
                    updateStatus('Authentication valid', 'success');
                    
                    const authInfo = document.getElementById('auth-info');
                    authInfo.style.display = 'block';
                    authInfo.className = 'status success';
                    authInfo.textContent = `Authenticated as: ${data.user.username} (${data.user.role})`;
                } else {
                    updateStatus('Authentication failed: ' + (data.error || 'Unknown error'), 'error');
                }
            } catch (error) {
                console.error('Auth check error:', error);
                updateStatus('Auth check failed: ' + error.message, 'error');
            }
            
            console.log('=== AUTH CHECK END ===');
        }

        async function testPing() {
            console.log('=== PING TEST START ===');
            updateStatus('Testing ping endpoint...', 'info');
            
            try {
                const response = await fetch('../api/mobile.php?action=ping');
                console.log('Ping response status:', response.status);
                
                const data = await response.json();
                console.log('Ping response data:', data);
                
                if (data.success) {
                    updateStatus('Ping successful - API is accessible', 'success');
                } else {
                    updateStatus('Ping failed: ' + (data.error || 'Unknown error'), 'error');
                }
            } catch (error) {
                console.error('Ping error:', error);
                updateStatus('Ping failed: ' + error.message, 'error');
            }
            
            console.log('=== PING TEST END ===');
        }

        async function testProjectsAPI() {
            console.log('=== PROJECTS API TEST START ===');
            updateStatus('Testing projects API directly...', 'info');
            
            const token = localStorage.getItem('flori_token');
            if (!token) {
                updateStatus('No token available - please login first', 'error');
                return;
            }
            
            try {
                const url = '../api/mobile.php?action=projects&page=1&limit=5';
                console.log('Testing URL:', url);
                
                const response = await fetch(url, {
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });
                
                console.log('Projects API response status:', response.status);
                const data = await response.json();
                console.log('Projects API response data:', data);
                
                if (data.success) {
                    const projectCount = data.data?.projects?.length || 0;
                    updateStatus(`Projects API working! Found ${projectCount} projects`, 'success');
                } else {
                    updateStatus('Projects API failed: ' + (data.error || 'Unknown error'), 'error');
                }
            } catch (error) {
                console.error('Projects API error:', error);
                updateStatus('Projects API failed: ' + error.message, 'error');
            }
            
            console.log('=== PROJECTS API TEST END ===');
        }

        async function testDirectFetch() {
            console.log('=== DIRECT FETCH TEST START ===');
            updateStatus('Testing direct fetch with floriAdmin.apiRequest...', 'info');
            
            if (!window.floriAdmin) {
                updateStatus('floriAdmin not available', 'error');
                return;
            }
            
            if (!window.floriAdmin.token) {
                updateStatus('No token in floriAdmin - please login first', 'error');
                return;
            }
            
            try {
                console.log('Using floriAdmin.apiRequest...');
                const response = await window.floriAdmin.apiRequest('mobile.php?action=projects&page=1&limit=5');
                console.log('Direct fetch response:', response);
                
                if (response && response.success) {
                    const projectCount = response.data?.projects?.length || 0;
                    updateStatus(`Direct fetch successful! Found ${projectCount} projects`, 'success');
                } else {
                    updateStatus('Direct fetch failed: ' + (response?.error || 'Unknown error'), 'error');
                }
            } catch (error) {
                console.error('Direct fetch error:', error);
                updateStatus('Direct fetch failed: ' + error.message, 'error');
            }
            
            console.log('=== DIRECT FETCH TEST END ===');
        }

        function initializeManager() {
            console.log('=== MANAGER INITIALIZATION START ===');
            updateStatus('Initializing ProjectsManager...', 'info');
            
            try {
                if (window.ProjectsManager) {
                    console.log('ProjectsManager already exists');
                    updateStatus('ProjectsManager already initialized', 'success');
                } else {
                    console.log('Creating new ProjectsManager...');
                    window.ProjectsManager = new ProjectsManager();
                    updateStatus('ProjectsManager created successfully', 'success');
                }
            } catch (error) {
                console.error('Manager initialization error:', error);
                updateStatus('Manager initialization failed: ' + error.message, 'error');
            }
            
            console.log('=== MANAGER INITIALIZATION END ===');
        }

        async function loadProjectsTest() {
            console.log('=== LOAD PROJECTS TEST START ===');
            updateStatus('Testing projects loading...', 'info');
            
            if (!window.ProjectsManager) {
                updateStatus('ProjectsManager not available - initializing...', 'warning');
                initializeManager();
                
                if (!window.ProjectsManager) {
                    updateStatus('Failed to initialize ProjectsManager', 'error');
                    return;
                }
            }
            
            try {
                await window.ProjectsManager.load();
                updateStatus('Projects loaded successfully!', 'success');
            } catch (error) {
                console.error('Load projects error:', error);
                updateStatus('Failed to load projects: ' + error.message, 'error');
            }
            
            console.log('=== LOAD PROJECTS TEST END ===');
        }

        function checkManagerStatus() {
            console.log('=== MANAGER STATUS CHECK START ===');
            
            if (window.ProjectsManager) {
                const status = {
                    isInitialized: window.ProjectsManager.isInitialized,
                    currentPage: window.ProjectsManager.currentPage,
                    itemsPerPage: window.ProjectsManager.itemsPerPage,
                    projectsCount: window.ProjectsManager.projects.length,
                    currentFilter: window.ProjectsManager.currentFilter,
                    currentSearch: window.ProjectsManager.currentSearch
                };
                
                console.log('ProjectsManager status:', status);
                updateStatus('ProjectsManager is available and initialized', 'success');
            } else {
                console.log('ProjectsManager not available');
                updateStatus('ProjectsManager not available', 'error');
            }
            
            console.log('=== MANAGER STATUS CHECK END ===');
        }

        // Initialize when page loads
        document.addEventListener('DOMContentLoaded', () => {
            console.log('=== PAGE LOADED ===');
            updateStatus('Test page loaded - ready for testing', 'info');
            
            // Check initial state
            setTimeout(() => {
                checkAuth();
                checkManagerStatus();
            }, 500);
        });
    </script>
</body>
</html>
